# SIMD Performance Alerting Rules
# Defines alerts for SIMD performance degradation and issues

groups:
  - name: simd_performance
    rules:
      # SIMD Utilization Alerts
      - alert: SIMDUtilizationLow
        expr: simd_utilization_percentage{time_window="1h"} < 50
        for: 5m
        labels:
          severity: warning
          component: simd
        annotations:
          summary: "SIMD utilization is below 50%"
          description: "SIMD utilization has been below 50% for the last hour. Current value: {{ $value }}%"
          runbook_url: "https://docs.legacybridge.com/runbooks/simd-utilization"

      - alert: SIMDUtilizationCriticallyLow
        expr: simd_utilization_percentage{time_window="1h"} < 20
        for: 2m
        labels:
          severity: critical
          component: simd
        annotations:
          summary: "SIMD utilization critically low"
          description: "SIMD utilization has dropped below 20%. This indicates potential SIMD feature detection issues or workload changes."
          runbook_url: "https://docs.legacybridge.com/runbooks/simd-critical"

      # SIMD Performance Degradation
      - alert: SIMDPerformanceDegradation
        expr: |
          (
            rate(simd_operation_duration_seconds_sum{simd_enabled="true"}[5m]) /
            rate(simd_operation_duration_seconds_count{simd_enabled="true"}[5m])
          ) > 
          (
            rate(simd_operation_duration_seconds_sum{simd_enabled="true"}[1h] offset 1h) /
            rate(simd_operation_duration_seconds_count{simd_enabled="true"}[1h] offset 1h)
          ) * 1.5
        for: 3m
        labels:
          severity: warning
          component: simd
        annotations:
          summary: "SIMD operation performance has degraded"
          description: "SIMD operations are taking 50% longer than the previous hour average"
          runbook_url: "https://docs.legacybridge.com/runbooks/simd-performance"

      # SIMD Feature Availability
      - alert: SIMDFeaturesUnavailable
        expr: simd_features_available{feature="avx2", available="true"} == 0
        for: 1m
        labels:
          severity: warning
          component: simd
        annotations:
          summary: "Optimal SIMD features (AVX2) not available"
          description: "AVX2 SIMD features are not detected. Performance may be suboptimal."
          runbook_url: "https://docs.legacybridge.com/runbooks/simd-features"

      - alert: SIMDFeaturesCompletelyUnavailable
        expr: cpu_simd_capability_score < 20
        for: 1m
        labels:
          severity: critical
          component: simd
        annotations:
          summary: "SIMD capabilities severely limited"
          description: "CPU SIMD capability score is below 20. SIMD optimizations may not be effective."
          runbook_url: "https://docs.legacybridge.com/runbooks/simd-capability"

      # SIMD Error Rate
      - alert: SIMDErrorRateHigh
        expr: rate(simd_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          component: simd
        annotations:
          summary: "High SIMD error rate detected"
          description: "SIMD error rate is above 0.1 errors per second over the last 5 minutes"
          runbook_url: "https://docs.legacybridge.com/runbooks/simd-errors"

      - alert: SIMDErrorRateCritical
        expr: rate(simd_errors_total[5m]) > 1.0
        for: 1m
        labels:
          severity: critical
          component: simd
        annotations:
          summary: "Critical SIMD error rate"
          description: "SIMD error rate is above 1.0 errors per second. SIMD functionality may be compromised."
          runbook_url: "https://docs.legacybridge.com/runbooks/simd-critical-errors"

  - name: simd_performance_targets
    rules:
      # Performance Target Monitoring
      - alert: SIMDPerformanceTargetMissed
        expr: simd_performance_improvement_percentage{document_size_category="small"} < 30
        for: 5m
        labels:
          severity: warning
          component: simd
        annotations:
          summary: "SIMD performance target missed for small documents"
          description: "SIMD performance improvement for small documents is below 30% target. Current: {{ $value }}%"
          runbook_url: "https://docs.legacybridge.com/runbooks/simd-targets"

      - alert: SIMDPerformanceTargetCriticallyMissed
        expr: simd_performance_improvement_percentage{document_size_category="small"} < 10
        for: 2m
        labels:
          severity: critical
          component: simd
        annotations:
          summary: "SIMD performance critically below target"
          description: "SIMD performance improvement for small documents is critically low: {{ $value }}%"
          runbook_url: "https://docs.legacybridge.com/runbooks/simd-critical-performance"

      # Document Processing Monitoring
      - alert: SIMDDocumentProcessingStalled
        expr: rate(documents_processed_total{simd_used="true"}[5m]) == 0
        for: 3m
        labels:
          severity: warning
          component: simd
        annotations:
          summary: "No SIMD document processing detected"
          description: "No documents have been processed with SIMD in the last 5 minutes"
          runbook_url: "https://docs.legacybridge.com/runbooks/simd-processing"

  - name: simd_system_health
    rules:
      # System-level SIMD health
      - alert: SIMDSystemOverload
        expr: |
          (
            rate(simd_operation_duration_seconds_count[5m]) > 100
          ) and (
            avg(simd_operation_duration_seconds{quantile="0.95"}) > 0.1
          )
        for: 2m
        labels:
          severity: warning
          component: simd
        annotations:
          summary: "SIMD system under high load"
          description: "High SIMD operation rate with increased latency detected"
          runbook_url: "https://docs.legacybridge.com/runbooks/simd-overload"

      # Memory and CPU correlation with SIMD
      - alert: SIMDMemoryPressure
        expr: |
          (
            container_memory_usage_bytes{container_label_com_docker_compose_service="legacybridge"} /
            container_spec_memory_limit_bytes{container_label_com_docker_compose_service="legacybridge"}
          ) > 0.85 and rate(simd_operation_duration_seconds_count[5m]) > 10
        for: 3m
        labels:
          severity: warning
          component: simd
        annotations:
          summary: "High memory usage during SIMD operations"
          description: "Memory usage is above 85% while processing SIMD operations"
          runbook_url: "https://docs.legacybridge.com/runbooks/simd-memory"

  - name: simd_recording_rules
    rules:
      # Recording rules for SIMD performance analysis
      - record: simd:operation_rate_5m
        expr: rate(simd_operation_duration_seconds_count[5m])

      - record: simd:operation_latency_p95_5m
        expr: histogram_quantile(0.95, rate(simd_operation_duration_seconds_bucket[5m]))

      - record: simd:operation_latency_p99_5m
        expr: histogram_quantile(0.99, rate(simd_operation_duration_seconds_bucket[5m]))

      - record: simd:utilization_avg_1h
        expr: avg_over_time(simd_utilization_percentage{time_window="1h"}[1h])

      - record: simd:performance_improvement_avg_1h
        expr: avg_over_time(simd_performance_improvement_percentage[1h])

      - record: simd:error_rate_5m
        expr: rate(simd_errors_total[5m])

      # SIMD efficiency metrics
      - record: simd:efficiency_score
        expr: |
          (
            simd_performance_improvement_percentage{document_size_category="small"} * 0.4 +
            simd_performance_improvement_percentage{document_size_category="medium"} * 0.3 +
            simd_utilization_percentage{time_window="1h"} * 0.2 +
            (100 - (rate(simd_errors_total[1h]) * 100)) * 0.1
          )
