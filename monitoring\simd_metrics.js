// SIMD Performance Monitoring Module
// Collects and exposes SIMD-specific performance metrics

const prometheus = require('prom-client');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class SIMDMetricsCollector {
    constructor() {
        this.register = new prometheus.Registry();
        this.initializeMetrics();
        this.setupPeriodicCollection();
    }

    initializeMetrics() {
        // SIMD feature availability metrics
        this.simdFeaturesGauge = new prometheus.Gauge({
            name: 'simd_features_available',
            help: 'SIMD features available on the system',
            labelNames: ['feature', 'available'],
            registers: [this.register]
        });

        // SIMD performance metrics
        this.simdPerformanceHistogram = new prometheus.Histogram({
            name: 'simd_operation_duration_seconds',
            help: 'Duration of SIMD operations in seconds',
            labelNames: ['operation_type', 'document_size', 'simd_enabled'],
            buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0],
            registers: [this.register]
        });

        // SIMD utilization metrics
        this.simdUtilizationGauge = new prometheus.Gauge({
            name: 'simd_utilization_percentage',
            help: 'Percentage of operations using SIMD acceleration',
            labelNames: ['time_window'],
            registers: [this.register]
        });

        // SIMD performance improvement metrics
        this.simdImprovementGauge = new prometheus.Gauge({
            name: 'simd_performance_improvement_percentage',
            help: 'Performance improvement percentage with SIMD vs scalar',
            labelNames: ['document_size_category', 'operation_type'],
            registers: [this.register]
        });

        // SIMD error metrics
        this.simdErrorCounter = new prometheus.Counter({
            name: 'simd_errors_total',
            help: 'Total number of SIMD-related errors',
            labelNames: ['error_type', 'fallback_used'],
            registers: [this.register]
        });

        // System CPU metrics for SIMD
        this.cpuSIMDCapabilityGauge = new prometheus.Gauge({
            name: 'cpu_simd_capability_score',
            help: 'CPU SIMD capability score (0-100)',
            registers: [this.register]
        });

        // Document processing metrics
        this.documentProcessingCounter = new prometheus.Counter({
            name: 'documents_processed_total',
            help: 'Total documents processed',
            labelNames: ['size_category', 'simd_used', 'conversion_type'],
            registers: [this.register]
        });
    }

    // Collect SIMD feature availability
    collectSIMDFeatures() {
        try {
            const features = this.detectSIMDFeatures();
            
            Object.entries(features).forEach(([feature, available]) => {
                this.simdFeaturesGauge
                    .labels(feature, available.toString())
                    .set(available ? 1 : 0);
            });

            // Calculate CPU SIMD capability score
            const score = this.calculateSIMDCapabilityScore(features);
            this.cpuSIMDCapabilityGauge.set(score);

        } catch (error) {
            console.error('Error collecting SIMD features:', error);
            this.simdErrorCounter
                .labels('feature_detection', 'false')
                .inc();
        }
    }

    // Detect available SIMD features
    detectSIMDFeatures() {
        const features = {
            sse2: false,
            sse4_2: false,
            avx2: false,
            avx512f: false
        };

        try {
            // Check if we're on x86_64 and can detect features
            if (process.arch === 'x64') {
                const cpuinfo = fs.readFileSync('/proc/cpuinfo', 'utf8');
                const flags = cpuinfo.match(/flags\s*:\s*(.+)/);
                
                if (flags && flags[1]) {
                    const flagList = flags[1].split(' ');
                    features.sse2 = flagList.includes('sse2');
                    features.sse4_2 = flagList.includes('sse4_2');
                    features.avx2 = flagList.includes('avx2');
                    features.avx512f = flagList.includes('avx512f');
                }
            }
        } catch (error) {
            console.warn('Could not detect SIMD features:', error.message);
        }

        return features;
    }

    // Calculate SIMD capability score
    calculateSIMDCapabilityScore(features) {
        let score = 0;
        
        if (features.sse2) score += 20;      // Basic SIMD support
        if (features.sse4_2) score += 25;    // Enhanced SIMD
        if (features.avx2) score += 40;      // Advanced SIMD (optimal)
        if (features.avx512f) score += 15;   // Latest SIMD features
        
        return score;
    }

    // Record SIMD operation performance
    recordSIMDOperation(operationType, documentSize, duration, simdEnabled) {
        const sizeCategory = this.categorizeDocumentSize(documentSize);
        
        this.simdPerformanceHistogram
            .labels(operationType, sizeCategory, simdEnabled.toString())
            .observe(duration);

        this.documentProcessingCounter
            .labels(sizeCategory, simdEnabled.toString(), operationType)
            .inc();
    }

    // Categorize document size for metrics
    categorizeDocumentSize(sizeBytes) {
        if (sizeBytes < 1024) return 'small';           // < 1KB
        if (sizeBytes < 10240) return 'medium';         // 1KB - 10KB
        if (sizeBytes < 102400) return 'large';         // 10KB - 100KB
        return 'xlarge';                                // > 100KB
    }

    // Calculate and update SIMD utilization
    updateSIMDUtilization() {
        try {
            // Get metrics from the last hour
            const metrics = this.calculateUtilizationMetrics();
            
            this.simdUtilizationGauge
                .labels('1h')
                .set(metrics.hourly);

            this.simdUtilizationGauge
                .labels('24h')
                .set(metrics.daily);

            // Update performance improvement metrics
            this.updatePerformanceImprovements(metrics);

        } catch (error) {
            console.error('Error updating SIMD utilization:', error);
            this.simdErrorCounter
                .labels('utilization_calculation', 'false')
                .inc();
        }
    }

    // Calculate utilization metrics
    calculateUtilizationMetrics() {
        // This would typically query a time-series database
        // For now, we'll simulate with reasonable values
        return {
            hourly: Math.random() * 20 + 75,  // 75-95% utilization
            daily: Math.random() * 15 + 80,   // 80-95% utilization
            improvements: {
                small: Math.random() * 10 + 90,    // 90-100% improvement
                medium: Math.random() * 15 + 80,   // 80-95% improvement
                large: Math.random() * 30 + 40,    // 40-70% improvement
                xlarge: Math.random() * 40 + 10    // 10-50% improvement
            }
        };
    }

    // Update performance improvement metrics
    updatePerformanceImprovements(metrics) {
        const operations = ['rtf_to_markdown', 'markdown_to_rtf'];
        
        Object.entries(metrics.improvements).forEach(([sizeCategory, improvement]) => {
            operations.forEach(operation => {
                this.simdImprovementGauge
                    .labels(sizeCategory, operation)
                    .set(improvement);
            });
        });
    }

    // Record SIMD error
    recordSIMDError(errorType, fallbackUsed = false) {
        this.simdErrorCounter
            .labels(errorType, fallbackUsed.toString())
            .inc();
    }

    // Setup periodic metric collection
    setupPeriodicCollection() {
        // Collect SIMD features every 5 minutes
        setInterval(() => {
            this.collectSIMDFeatures();
        }, 5 * 60 * 1000);

        // Update utilization metrics every minute
        setInterval(() => {
            this.updateSIMDUtilization();
        }, 60 * 1000);

        // Initial collection
        this.collectSIMDFeatures();
        this.updateSIMDUtilization();
    }

    // Get metrics for Prometheus scraping
    getMetrics() {
        return this.register.metrics();
    }

    // Generate SIMD monitoring report
    async generateMonitoringReport() {
        const metrics = await this.register.getMetricsAsJSON();
        
        const report = {
            timestamp: new Date().toISOString(),
            simd_status: {
                features_available: {},
                utilization: {},
                performance: {},
                errors: {}
            }
        };

        // Process metrics into report format
        metrics.forEach(metric => {
            if (metric.name.startsWith('simd_')) {
                const category = metric.name.split('_')[1];
                report.simd_status[category] = report.simd_status[category] || {};
                report.simd_status[category][metric.name] = metric.values;
            }
        });

        return report;
    }
}

// Export the collector
module.exports = SIMDMetricsCollector;
