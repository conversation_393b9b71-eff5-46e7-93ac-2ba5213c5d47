{"timestamp": "2025-07-31T16:28:21.119474500+00:00", "system_info": {"os": "windows", "arch": "x86_64", "cpu_count": 12, "rust_version": "unknown", "build_profile": "release"}, "simd_features": {"sse2": true, "sse4_2": true, "avx2": true, "avx512f": false}, "test_results": {"simd_performance": {"exit_code": 0, "stdout": "SIMD Performance Test Suite\n===========================\n\nTesting with 1KB documents\n--------------------------------------------------\nRTF document size: 651 bytes\nRTF to Markdown:\n  Scalar: 0.002967s ± 0.000396s\n  SIMD:   0.000074s ± 0.000020s\n  Improvement: 97.5%\n\nMarkdown document size: 668 bytes\nMarkdown to RTF:\n  Scalar: 0.003625s ± 0.000591s\n  SIMD:   0.000068s ± 0.000015s\n  Improvement: 98.1%\n\nCharacter Search Performance:\n  Scalar: 0.000000001s\n  SIMD (est): 0.000000000s\n  Improvement: 70.0%\n\nOverall Performance Summary:\n  Average improvement: 97.8%\n  ✓ Target 30-50% improvement ACHIEVED!\n\n\nTesting with 10KB documents\n--------------------------------------------------\nRTF document size: 5926 bytes\nRTF to Markdown:\n  Scalar: 0.004874s ± 0.000378s\n  SIMD:   0.000495s ± 0.000130s\n  Improvement: 89.8%\n\nMarkdown document size: 6577 bytes\nMarkdown to RTF:\n  Scalar: 0.003747s ± 0.000542s\n  SIMD:   0.000487s ± 0.000221s\n  Improvement: 87.0%\n\nCharacter Search Performance:\n  Scalar: 0.000000000s\n  SIMD (est): 0.000000000s\n  Improvement: NaN%\n\nOverall Performance Summary:\n  Average improvement: 88.4%\n  ✓ Target 30-50% improvement ACHIEVED!\n\n\nTesting with 100KB documents\n--------------------------------------------------\nRTF document size: 59126 bytes\nRTF to Markdown:\n  Scalar: 0.001923s ± 0.000354s\n  SIMD:   0.004872s ± 0.000776s\n  Improvement: -153.3%\n\nMarkdown document size: 66567 bytes\nMarkdown to RTF:\n  Scalar: 0.014194s ± 0.003305s\n  SIMD:   0.004741s ± 0.000991s\n  Improvement: 66.6%\n\nCharacter Search Performance:\n  Scalar: 0.000000001s\n  SIMD (est): 0.000000000s\n  Improvement: 70.0%\n\nOverall Performance Summary:\n  Average improvement: -43.4%\n  ✗ Target 30-50% improvement not yet reached\n\n\nTesting with 1000KB documents\n--------------------------------------------------\nRTF document size: 595626 bytes\nRTF to Markdown:\n  Scalar: 0.001653s ± 0.000307s\n  SIMD:   0.047429s ± 0.003390s\n  Improvement: -2769.8%\n\nMarkdown document size: 675467 bytes\nMarkdown to RTF:\n  Scalar: 0.061581s ± 0.004786s\n  SIMD:   0.072140s ± 0.005931s\n  Improvement: -17.1%\n\nCharacter Search Performance:\n  Scalar: 0.000000000s\n  SIMD (est): 0.000000000s\n  Improvement: NaN%\n\nOverall Performance Summary:\n  Average improvement: -1393.5%\n  ✗ Target 30-50% improvement not yet reached\n\n\nCPU SIMD Features:\n  SSE2:  true\n  SSE4.2: true\n  AVX2:  true\n", "stderr": "warning: unused import: `std::fmt`\n --> src\\conversion\\error.rs:5:5\n  |\n5 | use std::fmt;\n  |     ^^^^^^^^\n  |\n  = note: `#[warn(unused_imports)]` on by default\n\nwarning: unused imports: `AtomicUsize` and `Ordering`\n --> src\\conversion\\rtf_lexer.rs:4:25\n  |\n4 | use std::sync::atomic::{AtomicUsize, Ordering};\n  |                         ^^^^^^^^^^^  ^^^^^^^^\n\nwarning: unused import: `std::borrow::Cow`\n  --> src\\conversion\\rtf_parser_optimized.rs:15:5\n   |\n15 | use std::borrow::Cow;\n   |     ^^^^^^^^^^^^^^^^\n\nwarning: unused import: `std::sync::Arc`\n  --> src\\conversion\\rtf_parser_optimized.rs:16:5\n   |\n16 | use std::sync::Arc;\n   |     ^^^^^^^^^^^^^^\n\nwarning: unused import: `std::mem`\n  --> src\\conversion\\markdown_parser_optimized.rs:14:5\n   |\n14 | use std::mem;\n   |     ^^^^^^^^\n\nwarning: unused import: `escape_rtf_text_optimized`\n --> src\\conversion\\rtf_generator_optimized.rs:3:35\n  |\n3 | use super::rtf_escape_optimized::{escape_rtf_text_optimized, RtfEscaper};\n  |                                   ^^^^^^^^^^^^^^^^^^^^^^^^^\n\nwarning: unused import: `std::borrow::Cow`\n --> src\\conversion\\rtf_generator_optimized.rs:4:5\n  |\n4 | use std::borrow::Cow;\n  |     ^^^^^^^^^^^^^^^^\n\nwarning: unused import: `std::borrow::Cow`\n  --> src\\conversion\\markdown_parser_optimized_v2.rs:14:5\n   |\n14 | use std::borrow::Cow;\n   |     ^^^^^^^^^^^^^^^^\n\nwarning: unused import: `std::mem`\n  --> src\\conversion\\markdown_parser_optimized_v2.rs:15:5\n   |\n15 | use std::mem;\n   |     ^^^^^^^^\n\nwarning: unused import: `Mutex`\n --> src\\conversion\\memory_pools.rs:6:22\n  |\n6 | use std::sync::{Arc, Mutex};\n  |                      ^^^^^\n\nwarning: unused import: `std::collections::VecDeque`\n --> src\\conversion\\memory_pools.rs:7:5\n  |\n7 | use std::collections::VecDeque;\n  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nwarning: unused imports: `Color`, `FontInfo`, `TableCell`, and `TableRow`\n --> src\\conversion\\rtf_parser_pooled.rs:5:24\n  |\n5 |     RtfNode, RtfToken, TableRow, TableCell, FontInfo, Color,\n  |                        ^^^^^^^^  ^^^^^^^^^  ^^^^^^^^  ^^^^^\n\nwarning: unused import: `super::rtf_lexer`\n --> src\\conversion\\pooled_converter.rs:4:5\n  |\n4 | use super::rtf_lexer;\n  |     ^^^^^^^^^^^^^^^^\n\nwarning: unused import: `super::secure_parser::SecureRtfParser`\n  --> src\\conversion\\pooled_converter.rs:10:5\n   |\n10 | use super::secure_parser::SecureRtfParser;\n   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nwarning: unused import: `RtfDocument`\n --> src\\conversion\\simd_conversion.rs:4:55\n  |\n4 | use super::types::{ConversionError, ConversionResult, RtfDocument};\n  |                                                       ^^^^^^^^^^^\n\nwarning: unused import: `std::arch::x86_64::*`\n --> src\\conversion\\markdown_parser_simd.rs:9:5\n  |\n9 | use std::arch::x86_64::*;\n  |     ^^^^^^^^^^^^^^^^^^^^\n\nwarning: unused imports: `TableCell` and `TableRow`\n --> src\\conversion\\unified_parser.rs:9:24\n  |\n9 |     RtfNode, RtfToken, TableCell, TableRow,\n  |                        ^^^^^^^^^  ^^^^^^^^\n\nwarning: unused import: `Duration`\n  --> src\\conversion\\unified_parser.rs:14:17\n   |\n14 | use std::time::{Duration, Instant};\n   |                 ^^^^^^^^\n\nwarning: unused import: `SecurityLevel`\n --> src\\conversion\\unified_generator.rs:8:47\n  |\n8 | use super::unified_config::{ConversionConfig, SecurityLevel};\n  |                                               ^^^^^^^^^^^^^\n\nwarning: unused doc comment\n   --> src\\conversion\\unified_errors.rs:303:1\n    |\n303 | /// Thread-local storage for last error (for FFI)\n    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ rustdoc does not generate documentation for macro invocations\n    |\n    = help: to document an item produced by a macro, the macro must produce the documentation as part of its expansion\n    = note: `#[warn(unused_doc_comments)]` on by default\n\nwarning: unused import: `std::fmt`\n --> src\\conversion\\unified_error_system.rs:6:5\n  |\n6 | use std::fmt;\n  |     ^^^^^^^^\n\nwarning: unused import: `smallvec::SmallVec`\n  --> src\\pipeline\\formatting_engine_optimized.rs:15:5\n   |\n15 | use smallvec::SmallVec;\n   |     ^^^^^^^^^^^^^^^^^^\n\nwarning: unused imports: `Receiver` and `Sender`\n  --> src\\pipeline\\concurrent_processor.rs:13:34\n   |\n13 | use crossbeam_channel::{bounded, Sender, Receiver};\n   |                                  ^^^^^^  ^^^^^^^^\n\nwarning: unused import: `BackpressureError`\n  --> src\\pipeline\\concurrent_processor_v2.rs:10:71\n   |\n10 | use crate::pipeline::adaptive_thread_pool::{AdaptiveThreadPool, Task, BackpressureError, PoolConfig};\n   |                                                                       ^^^^^^^^^^^^^^^^^\n\nwarning: unused import: `Receiver`\n  --> src\\pipeline\\concurrent_processor_v2.rs:18:42\n   |\n18 | use crossbeam_channel::{bounded, Sender, Receiver};\n   |                                          ^^^^^^^^\n\nwarning: unused import: `uuid::Uuid`\n  --> src\\pipeline\\enhanced_batch_processor.rs:10:5\n   |\n10 | use uuid::Uuid;\n   |     ^^^^^^^^^^\n\nwarning: unused import: `ConversionResult`\n  --> src\\pipeline\\enhanced_batch_processor.rs:11:32\n   |\n11 | use crate::conversion::types::{ConversionResult, ConversionError};\n   |                                ^^^^^^^^^^^^^^^^\n\nwarning: unused import: `ConversionResponse`\n  --> src\\pipeline\\enhanced_batch_processor.rs:12:90\n   |\n12 | ...ConcurrentProcessorV2, ConversionRequest, ConversionResponse};\n   |                                              ^^^^^^^^^^^^^^^^^^\n\nwarning: unused import: `RtfParser`\n  --> src\\pipeline\\mod.rs:24:36\n   |\n24 | use crate::conversion::{rtf_lexer, RtfParser, SecureRtfParser, MarkdownGenerator, MarkdownParser, RtfGene...\n   |                                    ^^^^^^^^^\n\nwarning: unused doc comment\n   --> src\\ffi.rs:289:1\n    |\n289 | /// Thread-local error storage\n    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ rustdoc does not generate documentation for macro invocations\n    |\n    = help: to document an item produced by a macro, the macro must produce the documentation as part of its expansion\n\nwarning: unused imports: `CStr` and `CString`\n --> src\\ffi_legacy.rs:4:16\n  |\n4 | use std::ffi::{CStr, CString};\n  |                ^^^^  ^^^^^^^\n\nwarning: unused import: `c_void`\n --> src\\ffi_legacy.rs:5:35\n  |\n5 | use std::os::raw::{c_char, c_int, c_void};\n  |                                   ^^^^^^\n\nwarning: unused import: `std::collections::HashMap`\n --> src\\ffi_legacy.rs:9:5\n  |\n9 | use std::collections::HashMap;\n  |     ^^^^^^^^^^^^^^^^^^^^^^^^^\n\nwarning: unused import: `warn`\n --> src\\panic_handler.rs:7:22\n  |\n7 | use tracing::{error, warn};\n  |                      ^^^^\n\nwarning: variable does not need to be mutable\n   --> src\\pipeline\\enhanced_batch_processor.rs:245:29\n    |\n245 | ...   if let Some(mut progress) = processor.active_batches.write().await.get_mut(&batch_id_for_error) {\n    |                   ----^^^^^^^^\n    |                   |\n    |                   help: remove this `mut`\n    |\n    = note: `#[warn(unused_mut)]` on by default\n\nwarning: variable `completed` is assigned to, but never used\n   --> src\\pipeline\\enhanced_batch_processor.rs:401:17\n    |\n401 |         let mut completed = 0;\n    |                 ^^^^^^^^^\n    |\n    = note: consider using `_completed` instead\n    = note: `#[warn(unused_variables)]` on by default\n\nwarning: unused variable: `index`\n   --> src\\pipeline\\enhanced_batch_processor.rs:444:9\n    |\n444 |         index: usize,\n    |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_index`\n\nwarning: unused variable: `error`\n   --> src\\pipeline\\enhanced_batch_processor.rs:491:29\n    |\n491 |                         Err(error) => {\n    |                             ^^^^^ help: if this is intentional, prefix it with an underscore: `_error`\n\nwarning: variable does not need to be mutable\n   --> src\\pipeline\\enhanced_batch_processor.rs:443:9\n    |\n443 |         mut document: ConversionRequest,\n    |         ----^^^^^^^^\n    |         |\n    |         help: remove this `mut`\n\nwarning: variable does not need to be mutable\n   --> src\\pipeline\\enhanced_batch_processor.rs:195:13\n    |\n195 |         let mut progress = BatchProgress {\n    |             ----^^^^^^^^\n    |             |\n    |             help: remove this `mut`\n\nwarning: variable does not need to be mutable\n   --> src\\pipeline\\enhanced_batch_processor.rs:262:21\n    |\n262 |         if let Some(mut progress) = self.active_batches.write().await.get_mut(batch_id) {\n    |                     ----^^^^^^^^\n    |                     |\n    |                     help: remove this `mut`\n\nwarning: variable does not need to be mutable\n   --> src\\pipeline\\enhanced_batch_processor.rs:272:21\n    |\n272 |         if let Some(mut progress) = self.active_batches.write().await.get_mut(batch_id) {\n    |                     ----^^^^^^^^\n    |                     |\n    |                     help: remove this `mut`\n\nwarning: variable does not need to be mutable\n   --> src\\pipeline\\enhanced_batch_processor.rs:286:21\n    |\n286 |         if let Some(mut progress) = self.active_batches.write().await.get_mut(batch_id) {\n    |                     ----^^^^^^^^\n    |                     |\n    |                     help: remove this `mut`\n\nwarning: unused import: `std::fmt::Write`\n  --> src\\conversion\\unified_generator.rs:10:5\n   |\n10 | use std::fmt::Write;\n   |     ^^^^^^^^^^^^^^^\n\nwarning: unused variable: `i`\n   --> src\\conversion\\rtf_lexer.rs:216:13\n    |\n216 |         for i in 0..2 {\n    |             ^ help: if this is intentional, prefix it with an underscore: `_i`\n\nwarning: unused variable: `entry`\n   --> src\\conversion\\markdown_parser_optimized.rs:149:29\n    |\n149 |                 if let Some(entry) = self.cache.remove(&oldest_key) {\n    |                             ^^^^^ help: if this is intentional, prefix it with an underscore: `_entry`\n\nwarning: variable does not need to be mutable\n   --> src\\conversion\\markdown_parser_optimized.rs:327:29\n    |\n327 |                 if let Some(mut table_state) = self.table_state.take() {\n    |                             ----^^^^^^^^^^^\n    |                             |\n    |                             help: remove this `mut`\n\nwarning: variable does not need to be mutable\n   --> src\\conversion\\markdown_parser_optimized_v2.rs:199:29\n    |\n199 |                 if let Some(mut table_state) = self.table_state.take() {\n    |                             ----^^^^^^^^^^^\n    |                             |\n    |                             help: remove this `mut`\n\nwarning: variable does not need to be mutable\n --> src\\conversion\\rtf_lexer_pooled.rs:8:9\n  |\n8 |     let mut lexer = PooledRtfLexer::new(input);\n  |         ----^^^^^\n  |         |\n  |         help: remove this `mut`\n\nwarning: variable does not need to be mutable\n   --> src\\conversion\\markdown_parser_simd.rs:233:29\n    |\n233 |                 if let Some(mut table_state) = self.table_state.take() {\n    |                             ----^^^^^^^^^^^\n    |                             |\n    |                             help: remove this `mut`\n\nwarning: unused variable: `lf_vec`\n   --> src\\conversion\\markdown_parser_simd.rs:409:13\n    |\n409 |         let lf_vec = _mm256_set1_epi8(b'\\n' as i8);\n    |             ^^^^^^ help: if this is intentional, prefix it with an underscore: `_lf_vec`\n\nwarning: variable `found` is assigned to, but never used\n   --> src\\conversion\\markdown_simd_utils.rs:196:21\n    |\n196 |             let mut found = false;\n    |                     ^^^^^\n    |\n    = note: consider using `_found` instead\n\nwarning: value assigned to `found` is never read\n   --> src\\conversion\\markdown_simd_utils.rs:204:21\n    |\n204 |                     found = true;\n    |                     ^^^^^\n    |\n    = help: maybe it is overwritten before being read?\n    = note: `#[warn(unused_assignments)]` on by default\n\nwarning: unused variable: `limits`\n   --> src\\conversion\\unified_generator.rs:131:33\n    |\n131 |                     if let Some(limits) = self.config.security_level.limits() {\n    |                                 ^^^^^^ help: if this is intentional, prefix it with an underscore: `_limits`\n\nwarning: value assigned to `current_depth` is never read\n   --> src\\conversion\\enhanced_input_validation.rs:366:17\n    |\n366 |         let mut current_depth = 0;\n    |                 ^^^^^^^^^^^^^\n    |\n    = help: maybe it is overwritten before being read?\n\nwarning: unused variable: `parameter`\n   --> src\\pipeline\\formatting_engine_optimized.rs:193:9\n    |\n193 |         parameter: Option<i32>,\n    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_parameter`\n\nwarning: unused variable: `nodes`\n   --> src\\pipeline\\formatting_engine_optimized.rs:208:47\n    |\n208 | ...mized(&self, nodes: &[RtfNode], state: &OptimizedParserState) -> Option<RtfNode> {\n    |                 ^^^^^ help: if this is intentional, prefix it with an underscore: `_nodes`\n\nwarning: variable does not need to be mutable\n   --> src\\pipeline\\concurrent_processor.rs:236:13\n    |\n236 |         let mut peak_memory = 0;\n    |             ----^^^^^^^^^^^\n    |             |\n    |             help: remove this `mut`\n\nwarning: unused variable: `i`\n   --> src\\pipeline\\concurrent_processor.rs:358:14\n    |\n358 |         for (i, line) in content.lines().enumerate() {\n    |              ^ help: if this is intentional, prefix it with an underscore: `_i`\n\nwarning: unused variable: `numa_aware`\n   --> src\\pipeline\\adaptive_thread_pool.rs:244:13\n    |\n244 |         let numa_aware = self.config.numa_aware;\n    |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_numa_aware`\n\nwarning: unused variable: `recovery`\n   --> src\\pipeline\\mod.rs:375:13\n    |\n375 |         let recovery = error_recovery::ErrorRecovery::new();\n    |             ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_recovery`\n\nwarning: unused variable: `markdown_string`\n   --> src\\ffi.rs:490:9\n    |\n490 |     let markdown_string = match c_str_to_string(markdown_content) {\n    |         ^^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_markdown_string`\n\nwarning: unused variable: `temp`\n   --> src\\memory_pool_optimization.rs:502:9\n    |\n502 |         temp: &mut Vec<u8>,\n    |         ^^^^ help: if this is intentional, prefix it with an underscore: `_temp`\n\nwarning: unused variable: `default_hook`\n  --> src\\panic_handler.rs:23:9\n   |\n23 |     let default_hook = panic::take_hook();\n   |         ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_default_hook`\n\nwarning: variants `Underline` and `Code` are never constructed\n  --> src\\conversion\\markdown_parser.rs:37:5\n   |\n34 | enum FormattingState {\n   |      --------------- variants in this enum\n...\n37 |     Underline,\n   |     ^^^^^^^^^\n38 |     Code,\n   |     ^^^^\n   |\n   = note: `FormattingState` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n   = note: `#[warn(dead_code)]` on by default\n\nwarning: field `ordered` is never read\n  --> src\\conversion\\markdown_parser.rs:44:5\n   |\n42 | struct ListState {\n   |        --------- field in this struct\n43 |     level: u8,\n44 |     ordered: bool,\n   |     ^^^^^^^\n   |\n   = note: `ListState` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n\nwarning: field `last_accessed` is never read\n  --> src\\conversion\\markdown_parser_optimized.rs:70:5\n   |\n68 | struct CacheEntry {\n   |        ---------- field in this struct\n69 |     index: usize,\n70 |     last_accessed: u64,\n   |     ^^^^^^^^^^^^^\n   |\n   = note: `CacheEntry` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis\n\nwarning: methods `clear` and `stats` are never used\n   --> src\\conversion\\markdown_parser_optimized.rs:161:8\n    |\n78  | impl StringInterner {\n    | ------------------- methods in this implementation\n...\n161 |     fn clear(&mut self) {\n    |        ^^^^^\n...\n171 |     fn stats(&self) -> (u64, u64, f64, usize, usize) {\n    |        ^^^^^\n\nwarning: variants `Underline` and `Code` are never constructed\n   --> src\\conversion\\markdown_parser_optimized.rs:201:5\n    |\n198 | enum FormattingState {\n    |      --------------- variants in this enum\n...\n201 |     Underline,\n    |     ^^^^^^^^^\n202 |     Code,\n    |     ^^^^\n    |\n    = note: `FormattingState` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n\nwarning: field `ordered` is never read\n   --> src\\conversion\\markdown_parser_optimized.rs:208:5\n    |\n206 | struct ListState {\n    |        --------- field in this struct\n207 |     level: u8,\n208 |     ordered: bool,\n    |     ^^^^^^^\n    |\n    = note: `ListState` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n\nwarning: field `last_accessed` is never read\n  --> src\\conversion\\string_interner_optimized.rs:21:5\n   |\n19 | struct CacheEntry {\n   |        ---------- field in this struct\n20 |     index: usize,\n21 |     last_accessed: u64,\n   |     ^^^^^^^^^^^^^\n   |\n   = note: `CacheEntry` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis\n\nwarning: field `string_cache` is never read\n  --> src\\conversion\\markdown_parser_optimized_v2.rs:64:5\n   |\n56 | struct OptimizedConverterV2<'a, 'b> {\n   |        -------------------- field in this struct\n...\n64 |     string_cache: &'b mut OptimizedStringInterner<'a>,\n   |     ^^^^^^^^^^^^\n\nwarning: variants `Underline` and `Code` are never constructed\n  --> src\\conversion\\markdown_parser_optimized_v2.rs:73:5\n   |\n70 | enum FormattingState {\n   |      --------------- variants in this enum\n...\n73 |     Underline,\n   |     ^^^^^^^^^\n74 |     Code,\n   |     ^^^^\n   |\n   = note: `FormattingState` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n\nwarning: field `ordered` is never read\n  --> src\\conversion\\markdown_parser_optimized_v2.rs:80:5\n   |\n78 | struct ListState {\n   |        --------- field in this struct\n79 |     level: u8,\n80 |     ordered: bool,\n   |     ^^^^^^^\n   |\n   = note: `ListState` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n\nwarning: field `memory_used` is never read\n  --> src\\conversion\\rtf_parser_pooled.rs:27:5\n   |\n19 | pub struct PooledRtfParser {\n   |            --------------- field in this struct\n...\n27 |     memory_used: usize,\n   |     ^^^^^^^^^^^\n\nwarning: fields `has_sse42` and `has_avx2` are never read\n  --> src\\conversion\\simd_conversion.rs:16:5\n   |\n14 | struct SimdFeatures {\n   |        ------------ fields in this struct\n15 |     has_sse2: bool,\n16 |     has_sse42: bool,\n   |     ^^^^^^^^^\n17 |     has_avx2: bool,\n   |     ^^^^^^^^\n\nwarning: fields `special_char_positions` and `next_special_idx` are never read\n  --> src\\conversion\\markdown_parser_simd.rs:85:5\n   |\n75 | struct SimdConverter<'a> {\n   |        ------------- fields in this struct\n...\n85 |     special_char_positions: Vec<usize>,\n   |     ^^^^^^^^^^^^^^^^^^^^^^\n86 |     next_special_idx: usize,\n   |     ^^^^^^^^^^^^^^^^\n\nwarning: variants `Underline` and `Code` are never constructed\n  --> src\\conversion\\markdown_parser_simd.rs:93:5\n   |\n90 | enum FormattingState {\n   |      --------------- variants in this enum\n...\n93 |     Underline,\n   |     ^^^^^^^^^\n94 |     Code,\n   |     ^^^^\n   |\n   = note: `FormattingState` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n\nwarning: field `ordered` is never read\n   --> src\\conversion\\markdown_parser_simd.rs:100:5\n    |\n98  | struct ListState {\n    |        --------- field in this struct\n99  |     level: u8,\n100 |     ordered: bool,\n    |     ^^^^^^^\n    |\n    = note: `ListState` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n\nwarning: fields `font_table_mode` and `color_table_mode` are never read\n   --> src\\conversion\\unified_parser.rs:313:5\n    |\n309 | struct RtfParserState {\n    |        -------------- fields in this struct\n...\n313 |     font_table_mode: bool,\n    |     ^^^^^^^^^^^^^^^\n314 |     color_table_mode: bool,\n    |     ^^^^^^^^^^^^^^^^\n\nwarning: variant `Code` is never constructed\n   --> src\\conversion\\unified_parser.rs:359:5\n    |\n356 | enum FormattingState {\n    |      --------------- variant in this enum\n...\n359 |     Code,\n    |     ^^^^\n    |\n    = note: `FormattingState` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis\n\nwarning: field `ordered` is never read\n   --> src\\conversion\\unified_parser.rs:365:5\n    |\n363 | struct ListState {\n    |        --------- field in this struct\n364 |     level: u8,\n365 |     ordered: bool,\n    |     ^^^^^^^\n    |\n    = note: `ListState` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis\n\nwarning: field `allowed_protocols` is never read\n  --> src\\conversion\\enhanced_input_validation.rs:48:5\n   |\n44 | pub struct EnhancedInputValidator {\n   |            ---------------------- field in this struct\n...\n48 |     allowed_protocols: HashSet<String>,\n   |     ^^^^^^^^^^^^^^^^^\n\nwarning: fields `in_stylesheet`, `style_definitions`, `list_definitions`, and `current_list_level` are never read\n   --> src\\pipeline\\formatting_engine.rs:105:5\n    |\n99  | struct ParserState {\n    |        ----------- fields in this struct\n...\n105 |     in_stylesheet: bool,\n    |     ^^^^^^^^^^^^^\n106 |     style_definitions: HashMap<i32, StyleDefinition>,\n    |     ^^^^^^^^^^^^^^^^^\n107 |     list_definitions: HashMap<i32, ListDefinition>,\n    |     ^^^^^^^^^^^^^^^^\n108 |     current_list_level: u8,\n    |     ^^^^^^^^^^^^^^^^^^\n    |\n    = note: `ParserState` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n\nwarning: fields `name`, `base_style`, and `formatting` are never read\n   --> src\\pipeline\\formatting_engine.rs:114:5\n    |\n113 | struct StyleDefinition {\n    |        --------------- fields in this struct\n114 |     name: String,\n    |     ^^^^\n115 |     base_style: Option<i32>,\n    |     ^^^^^^^^^^\n116 |     formatting: NodeFormatting,\n    |     ^^^^^^^^^^\n    |\n    = note: `StyleDefinition` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n\nwarning: fields `id` and `levels` are never read\n   --> src\\pipeline\\formatting_engine.rs:121:5\n    |\n120 | struct ListDefinition {\n    |        -------------- fields in this struct\n121 |     id: i32,\n    |     ^^\n122 |     levels: Vec<ListLevelDefinition>,\n    |     ^^^^^^\n    |\n    = note: `ListDefinition` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n\nwarning: fields `level`, `list_type`, `format_string`, and `indent` are never read\n   --> src\\pipeline\\formatting_engine.rs:127:5\n    |\n126 | struct ListLevelDefinition {\n    |        ------------------- fields in this struct\n127 |     level: u8,\n    |     ^^^^^\n128 |     list_type: ListType,\n    |     ^^^^^^^^^\n129 |     format_string: String,\n    |     ^^^^^^^^^^^^^\n130 |     indent: i32,\n    |     ^^^^^^\n    |\n    = note: `ListLevelDefinition` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n\nwarning: field `cell_formatting` is never read\n   --> src\\pipeline\\formatting_engine.rs:137:5\n    |\n134 | struct TableContext {\n    |        ------------ field in this struct\n...\n137 |     cell_formatting: Vec<NodeFormatting>,\n    |     ^^^^^^^^^^^^^^^\n    |\n    = note: `TableContext` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n\nwarning: field `support_embedded_objects` is never read\n   --> src\\pipeline\\formatting_engine.rs:143:5\n    |\n141 | pub struct FormattingEngine {\n    |            ---------------- field in this struct\n142 |     preserve_custom_properties: bool,\n143 |     support_embedded_objects: bool,\n    |     ^^^^^^^^^^^^^^^^^^^^^^^^\n\nwarning: fields `list_stack` and `preserve_spacing` are never read\n   --> src\\pipeline\\formatting_engine.rs:571:5\n    |\n569 | struct MarkdownContext {\n    |        --------------- fields in this struct\n570 |     in_table: bool,\n571 |     list_stack: Vec<ListType>,\n    |     ^^^^^^^^^^\n572 |     preserve_spacing: bool,\n    |     ^^^^^^^^^^^^^^^^\n\nwarning: field `format_cache` is never read\n  --> src\\pipeline\\formatting_engine_optimized.rs:25:5\n   |\n19 | pub struct OptimizedFormattingEngine {\n   |            ------------------------- field in this struct\n...\n25 |     format_cache: FormatCache,\n   |     ^^^^^^^^^^^^\n\nwarning: field `rows` is never read\n   --> src\\pipeline\\formatting_engine_optimized.rs:555:5\n    |\n553 | struct TableBuilder {\n    |        ------------ field in this struct\n554 |     current_row: Vec<TableCell>,\n555 |     rows: Vec<TableRow>,\n    |     ^^^^\n\nwarning: fields `in_table` and `list_depth` are never read\n   --> src\\pipeline\\formatting_engine_optimized.rs:589:5\n    |\n588 | struct MarkdownContext {\n    |        --------------- fields in this struct\n589 |     in_table: bool,\n    |     ^^^^^^^^\n590 |     list_depth: u8,\n    |     ^^^^^^^^^^\n\nwarning: field `cached_formats` is never read\n   --> src\\pipeline\\formatting_engine_optimized.rs:604:5\n    |\n603 | struct FormatCache {\n    |        ----------- field in this struct\n604 |     cached_formats: AHashMap<NodeFormatting, String>,\n    |     ^^^^^^^^^^^^^^\n\nwarning: field `position` is never read\n  --> src\\pipeline\\error_recovery.rs:42:5\n   |\n41 | struct ErrorLocation {\n   |        ------------- field in this struct\n42 |     position: usize,\n   |     ^^^^^^^^\n   |\n   = note: `ErrorLocation` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n\nwarning: fields `max_recovery_attempts` and `preserve_as_much_as_possible` are never read\n  --> src\\pipeline\\error_recovery.rs:50:5\n   |\n48 | pub struct ErrorRecovery {\n   |            ------------- fields in this struct\n49 |     strategy: RecoveryStrategy,\n50 |     max_recovery_attempts: usize,\n   |     ^^^^^^^^^^^^^^^^^^^^^\n51 |     preserve_as_much_as_possible: bool,\n   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nwarning: field `template_dir` is never read\n   --> src\\pipeline\\template_system.rs:307:5\n    |\n305 | pub struct TemplateSystem {\n    |            -------------- field in this struct\n306 |     templates: HashMap<String, DocumentTemplate>,\n307 |     template_dir: Option<String>,\n    |     ^^^^^^^^^^^^\n\nwarning: field `id` is never read\n  --> src\\pipeline\\adaptive_thread_pool.rs:85:5\n   |\n84 | struct WorkerState {\n   |        ----------- field in this struct\n85 |     id: usize,\n   |     ^^\n\nwarning: associated function `set_numa_affinity` is never used\n   --> src\\pipeline\\adaptive_thread_pool.rs:431:8\n    |\n106 | impl AdaptiveThreadPool {\n    | ----------------------- associated function in this implementation\n...\n431 |     fn set_numa_affinity(_worker_id: usize) {\n    |        ^^^^^^^^^^^^^^^^^\n\nwarning: fields `small_buffers`, `medium_buffers`, and `large_buffers` are never read\n  --> src\\pipeline\\concurrent_processor_v2.rs:80:5\n   |\n79 | struct MemoryPool {\n   |        ---------- fields in this struct\n80 |     small_buffers: RwLock<Vec<Vec<u8>>>,  // < 1MB\n   |     ^^^^^^^^^^^^^\n81 |     medium_buffers: RwLock<Vec<Vec<u8>>>, // 1-10MB\n   |     ^^^^^^^^^^^^^^\n82 |     large_buffers: RwLock<Vec<Vec<u8>>>,  // > 10MB\n   |     ^^^^^^^^^^^^^\n\nwarning: methods `acquire` and `release` are never used\n   --> src\\pipeline\\concurrent_processor_v2.rs:98:8\n    |\n87  | impl MemoryPool {\n    | --------------- methods in this implementation\n...\n98  |     fn acquire(&self, size: usize) -> Vec<u8> {\n    |        ^^^^^^^\n...\n118 |     fn release(&self, buffer: Vec<u8>) {\n    |        ^^^^^^^\n\nwarning: function `set_and_return_error` is never used\n   --> src\\ffi_error_bridge.rs:123:15\n    |\n123 | pub(crate) fn set_and_return_error(error: LegacyBridgeError) -> i32 {\n    |               ^^^^^^^^^^^^^^^^^^^^\n\nwarning: function `handle_conversion_error` is never used\n   --> src\\ffi_error_bridge.rs:130:15\n    |\n130 | pub(crate) fn handle_conversion_error(err: crate::conversion::types::ConversionError) -> i32 {\n    |               ^^^^^^^^^^^^^^^^^^^^^^^\n\nwarning: function `create_parse_error` is never used\n   --> src\\ffi_error_bridge.rs:136:15\n    |\n136 | pub(crate) fn create_parse_error(\n    |               ^^^^^^^^^^^^^^^^^^\n\nwarning: function `create_io_error` is never used\n   --> src\\ffi_error_bridge.rs:153:15\n    |\n153 | pub(crate) fn create_io_error(\n    |               ^^^^^^^^^^^^^^^\n\nwarning: function `create_conversion_error` is never used\n   --> src\\ffi_error_bridge.rs:167:15\n    |\n167 | pub(crate) fn create_conversion_error(\n    |               ^^^^^^^^^^^^^^^^^^^^^^^\n\nwarning: constant `SECTOR_SIZE` is never used\n  --> src\\formats\\doc.rs:11:7\n   |\n11 | const SECTOR_SIZE: usize = 512;\n   |       ^^^^^^^^^^^\n\nwarning: constant `MINI_SECTOR_SIZE` is never used\n  --> src\\formats\\doc.rs:12:7\n   |\n12 | const MINI_SECTOR_SIZE: usize = 64;\n   |       ^^^^^^^^^^^^^^^^\n\nwarning: multiple fields are never read\n  --> src\\formats\\doc.rs:22:5\n   |\n16 | struct DocHeader {\n   |        --------- fields in this struct\n...\n22 |     mini_sector_size: u16,\n   |     ^^^^^^^^^^^^^^^^\n23 |     num_dir_sectors: u32,\n   |     ^^^^^^^^^^^^^^^\n24 |     num_fat_sectors: u32,\n   |     ^^^^^^^^^^^^^^^\n25 |     dir_first_sector: u32,\n26 |     mini_stream_cutoff: u32,\n   |     ^^^^^^^^^^^^^^^^^^\n27 |     mini_fat_first_sector: u32,\n   |     ^^^^^^^^^^^^^^^^^^^^^\n28 |     num_mini_fat_sectors: u32,\n   |     ^^^^^^^^^^^^^^^^^^^^\n29 |     difat_first_sector: u32,\n   |     ^^^^^^^^^^^^^^^^^^\n30 |     num_difat_sectors: u32,\n   |     ^^^^^^^^^^^^^^^^^\n   |\n   = note: `DocHeader` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\n\nwarning: fields `name_length`, `entry_type`, `color`, `left_sibling`, `right_sibling`, and `child` are never read\n  --> src\\formats\\doc.rs:71:5\n   |\n69 | struct DirectoryEntry {\n   |        -------------- fields in this struct\n70 |     name: String,\n71 |     name_length: u16,\n   |     ^^^^^^^^^^^\n72 |     entry_type: u8,\n   |     ^^^^^^^^^^\n73 |     color: u8,\n   |     ^^^^^\n74 |     left_sibling: u32,\n   |     ^^^^^^^^^^^^\n75 |     right_sibling: u32,\n   |     ^^^^^^^^^^^^^\n76 |     child: u32,\n   |     ^^^^^\n   |\n   = note: `DirectoryEntry` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\n\nwarning: method `is_stream` is never used\n   --> src\\formats\\doc.rs:106:8\n    |\n81  | impl DirectoryEntry {\n    | ------------------- method in this implementation\n...\n106 |     fn is_stream(&self) -> bool {\n    |        ^^^^^^^^^\n\nwarning: constant `WP_SPACE` is never used\n  --> src\\formats\\wordperf.rs:18:7\n   |\n18 | const WP_SPACE: u8 = 0x20;\n   |       ^^^^^^^^\n\nwarning: fields `version`, `file_type`, and `revision` are never read\n  --> src\\formats\\wordperf.rs:26:5\n   |\n24 | struct WordPerfectHeader {\n   |        ----------------- fields in this struct\n25 |     signature: [u8; 4],\n26 |     version: u32,\n   |     ^^^^^^^\n...\n30 |     file_type: u16,\n   |     ^^^^^^^^^\n...\n33 |     revision: u16,\n   |     ^^^^^^^^\n   |\n   = note: `WordPerfectHeader` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\n\nwarning: fields `code`, `subcode`, and `data` are never read\n  --> src\\formats\\wordperf.rs:78:5\n   |\n77 | struct WpFunction {\n   |        ---------- fields in this struct\n78 |     code: u8,\n   |     ^^^^\n79 |     subcode: Option<u8>,\n   |     ^^^^^^^\n80 |     data: Vec<u8>,\n   |     ^^^^\n   |\n   = note: `WpFunction` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n\nwarning: fields `reserved`, `table_flags`, and `code_page` are never read\n  --> src\\formats\\dbase.rs:72:5\n   |\n66 | struct DBaseHeader {\n   |        ----------- fields in this struct\n...\n72 |     reserved: [u8; 16],\n   |     ^^^^^^^^\n73 |     table_flags: u8,\n   |     ^^^^^^^^^^^\n74 |     code_page: u8,\n   |     ^^^^^^^^^\n   |\n   = note: `DBaseHeader` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\n\nwarning: field `flags` is never read\n   --> src\\formats\\dbase.rs:137:5\n    |\n132 | struct FieldDescriptor {\n    |        --------------- field in this struct\n...\n137 |     flags: u8,\n    |     ^^^^^\n    |\n    = note: `FieldDescriptor` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n\nwarning: constant `WS_PRINT_CONTROLS` is never used\n  --> src\\formats\\wordstar.rs:12:7\n   |\n12 | const WS_PRINT_CONTROLS: u8 = 0x1D;\n   |       ^^^^^^^^^^^^^^^^^\n\nwarning: field `0` is never read\n  --> src\\formats\\wordstar.rs:18:10\n   |\n18 |     Bold(bool),           // ^B (bold on/off)\n   |     ---- ^^^^\n   |     |\n   |     field in this variant\n   |\n   = note: `WordStarControl` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\nhelp: consider changing the field to be of unit type to suppress this warning while preserving the field numbering, or remove the field\n   |\n18 -     Bold(bool),           // ^B (bold on/off)\n18 +     Bold(()),           // ^B (bold on/off)\n   |\n\nwarning: field `0` is never read\n  --> src\\formats\\wordstar.rs:19:15\n   |\n19 |     Underline(bool),      // ^S (underline on/off)\n   |     --------- ^^^^\n   |     |\n   |     field in this variant\n   |\n   = note: `WordStarControl` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\nhelp: consider changing the field to be of unit type to suppress this warning while preserving the field numbering, or remove the field\n   |\n19 -     Underline(bool),      // ^S (underline on/off)\n19 +     Underline(()),      // ^S (underline on/off)\n   |\n\nwarning: field `0` is never read\n  --> src\\formats\\wordstar.rs:20:12\n   |\n20 |     Italic(bool),         // ^Y (italic on/off)\n   |     ------ ^^^^\n   |     |\n   |     field in this variant\n   |\n   = note: `WordStarControl` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\nhelp: consider changing the field to be of unit type to suppress this warning while preserving the field numbering, or remove the field\n   |\n20 -     Italic(bool),         // ^Y (italic on/off)\n20 +     Italic(()),         // ^Y (italic on/off)\n   |\n\nwarning: field `0` is never read\n  --> src\\formats\\wordstar.rs:21:17\n   |\n21 |     Superscript(bool),    // ^T (superscript on/off)\n   |     ----------- ^^^^\n   |     |\n   |     field in this variant\n   |\n   = note: `WordStarControl` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\nhelp: consider changing the field to be of unit type to suppress this warning while preserving the field numbering, or remove the field\n   |\n21 -     Superscript(bool),    // ^T (superscript on/off)\n21 +     Superscript(()),    // ^T (superscript on/off)\n   |\n\nwarning: field `0` is never read\n  --> src\\formats\\wordstar.rs:22:15\n   |\n22 |     Subscript(bool),      // ^V (subscript on/off)\n   |     --------- ^^^^\n   |     |\n   |     field in this variant\n   |\n   = note: `WordStarControl` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\nhelp: consider changing the field to be of unit type to suppress this warning while preserving the field numbering, or remove the field\n   |\n22 -     Subscript(bool),      // ^V (subscript on/off)\n22 +     Subscript(()),      // ^V (subscript on/off)\n   |\n\nwarning: field `0` is never read\n  --> src\\formats\\wordstar.rs:28:18\n   |\n28 |     PrintControl(u8),     // Other print controls\n   |     ------------ ^^\n   |     |\n   |     field in this variant\n   |\n   = note: `WordStarControl` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\nhelp: consider changing the field to be of unit type to suppress this warning while preserving the field numbering, or remove the field\n   |\n28 -     PrintControl(u8),     // Other print controls\n28 +     PrintControl(()),     // Other print controls\n   |\n\nwarning: field `metadata` is never read\n  --> src\\formats\\wordstar.rs:36:5\n   |\n34 | struct WordStarDocument {\n   |        ---------------- field in this struct\n35 |     content: Vec<WordStarControl>,\n36 |     metadata: HashMap<String, String>,\n   |     ^^^^^^^^\n   |\n   = note: `WordStarDocument` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\n\nwarning: method `add_metadata` is never used\n  --> src\\formats\\wordstar.rs:51:8\n   |\n39 | impl WordStarDocument {\n   | --------------------- method in this implementation\n...\n51 |     fn add_metadata(&mut self, key: String, value: String) {\n   |        ^^^^^^^^^^^^\n\nwarning: constant `LOTUS_CALCORDER` is never used\n  --> src\\formats\\lotus.rs:18:7\n   |\n18 | const LOTUS_CALCORDER: u16 = 0x0003;\n   |       ^^^^^^^^^^^^^^^\n\nwarning: constant `LOTUS_SPLIT` is never used\n  --> src\\formats\\lotus.rs:19:7\n   |\n19 | const LOTUS_SPLIT: u16 = 0x0004;\n   |       ^^^^^^^^^^^\n\nwarning: constant `LOTUS_SYNC` is never used\n  --> src\\formats\\lotus.rs:20:7\n   |\n20 | const LOTUS_SYNC: u16 = 0x0005;\n   |       ^^^^^^^^^^\n\nwarning: constant `LOTUS_RANGE` is never used\n  --> src\\formats\\lotus.rs:21:7\n   |\n21 | const LOTUS_RANGE: u16 = 0x0006;\n   |       ^^^^^^^^^^^\n\nwarning: constant `LOTUS_COLW1` is never used\n  --> src\\formats\\lotus.rs:23:7\n   |\n23 | const LOTUS_COLW1: u16 = 0x0008;\n   |       ^^^^^^^^^^^\n\nwarning: constant `LOTUS_WINTWO` is never used\n  --> src\\formats\\lotus.rs:24:7\n   |\n24 | const LOTUS_WINTWO: u16 = 0x0009;\n   |       ^^^^^^^^^^^^\n\nwarning: constant `LOTUS_COLW2` is never used\n  --> src\\formats\\lotus.rs:25:7\n   |\n25 | const LOTUS_COLW2: u16 = 0x000A;\n   |       ^^^^^^^^^^^\n\nwarning: constant `LOTUS_NAME` is never used\n  --> src\\formats\\lotus.rs:26:7\n   |\n26 | const LOTUS_NAME: u16 = 0x000B;\n   |       ^^^^^^^^^^\n\nwarning: field `format` is never read\n  --> src\\formats\\lotus.rs:76:5\n   |\n73 | struct LotusCell {\n   |        --------- field in this struct\n...\n76 |     format: u8,\n   |     ^^^^^^\n   |\n   = note: `LotusCell` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n\nwarning: field `length` is never read\n   --> src\\formats\\lotus.rs:113:5\n    |\n111 | struct LotusRecord {\n    |        ----------- field in this struct\n112 |     record_type: u16,\n113 |     length: u16,\n    |     ^^^^^^\n    |\n    = note: `LotusRecord` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\n\nwarning: field `max_size` is never read\n  --> src\\memory_pool_optimization.rs:43:5\n   |\n40 | pub struct ObjectPool<T> {\n   |            ---------- field in this struct\n...\n43 |     max_size: usize,\n   |     ^^^^^^^^\n\nwarning: `legacybridge` (lib) generated 136 warnings (run `cargo fix --lib -p legacybridge` to apply 43 suggestions)\n   Compiling legacybridge v0.0.0 (C:\\dev\\legacy-bridge\\legacybridge\\src-tauri)\nwarning: unused import: `std::fs`\n --> test_simd_performance.rs:5:5\n  |\n5 | use std::fs;\n  |     ^^^^^^^\n  |\n  = note: `#[warn(unused_imports)]` on by default\n\nwarning: unused variable: `name`\n  --> test_simd_performance.rs:57:28\n   |\n57 | fn benchmark_conversion<F>(name: &str, iterations: usize, mut f: F) -> (f64, f64)\n   |                            ^^^^ help: if this is intentional, prefix it with an underscore: `_name`\n   |\n   = note: `#[warn(unused_variables)]` on by default\n\nwarning: variable `scalar_count` is assigned to, but never used\n   --> test_simd_performance.rs:147:17\n    |\n147 |         let mut scalar_count = 0;\n    |                 ^^^^^^^^^^^^\n    |\n    = note: consider using `_scalar_count` instead\n\nwarning: `legacybridge` (bin \"test_simd_performance\") generated 3 warnings (run `cargo fix --bin \"test_simd_performance\"` to apply 1 suggestion)\n    Finished `release` profile [optimized] target(s) in 25.49s\n     Running `target\\release\\test_simd_performance.exe`\n", "success": true}, "security_validation": {"exit_code": 0, "tests_passed": 0, "success": true, "security_maintained": false}}, "performance_metrics": {"simd_tests_total": 0, "simd_tests_passed": 0, "simd_test_success_rate": 0.0, "performance_baseline_established": true, "optimization_level": "release", "target_architecture": "x86_64"}, "recommendations": ["✅ AVX2 support detected - optimal SIMD performance available", "❌ Security validation failed - review SIMD implementation", "🔧 Continue monitoring SIMD performance in production", "📊 Establish performance baselines for different document sizes", "🚀 Consider additional SIMD optimizations for string processing"]}