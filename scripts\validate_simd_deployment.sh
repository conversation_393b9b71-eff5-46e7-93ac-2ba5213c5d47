#!/bin/bash

# SIMD Deployment Validation Script
# Validates SIMD functionality in production deployment

set -euo pipefail

echo "🚀 SIMD Deployment Validation"
echo "============================="

# Configuration
CONTAINER_NAME="${CONTAINER_NAME:-legacybridge-app}"
TIMEOUT="${TIMEOUT:-60}"
VALIDATION_RESULTS_FILE="simd_deployment_validation.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Initialize validation results
init_validation_results() {
    cat > "$VALIDATION_RESULTS_FILE" << EOF
{
    "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
    "deployment_validation": {
        "container_status": "unknown",
        "simd_features": {},
        "performance_tests": {},
        "security_validation": {},
        "overall_status": "unknown"
    }
}
EOF
}

# Check if container is running
check_container_status() {
    log_info "Checking container status..."
    
    if docker ps --format "table {{.Names}}" | grep -q "$CONTAINER_NAME"; then
        log_success "Container $CONTAINER_NAME is running"
        jq '.deployment_validation.container_status = "running"' "$VALIDATION_RESULTS_FILE" > tmp.json && mv tmp.json "$VALIDATION_RESULTS_FILE"
        return 0
    else
        log_error "Container $CONTAINER_NAME is not running"
        jq '.deployment_validation.container_status = "not_running"' "$VALIDATION_RESULTS_FILE" > tmp.json && mv tmp.json "$VALIDATION_RESULTS_FILE"
        return 1
    fi
}

# Validate SIMD features in container
validate_simd_features() {
    log_info "Validating SIMD features in container..."
    
    # Check if the container has the SIMD binary
    if docker exec "$CONTAINER_NAME" test -f /app/bin/legacybridge 2>/dev/null; then
        log_success "SIMD-optimized binary found in container"
        
        # Test SIMD feature detection
        local simd_output
        simd_output=$(docker exec "$CONTAINER_NAME" /app/bin/legacybridge --simd-info 2>/dev/null || echo "SIMD info not available")
        
        # Update results
        jq --arg output "$simd_output" '.deployment_validation.simd_features = {
            "binary_present": true,
            "feature_detection": $output
        }' "$VALIDATION_RESULTS_FILE" > tmp.json && mv tmp.json "$VALIDATION_RESULTS_FILE"
        
        return 0
    else
        log_warning "SIMD binary not found, checking Node.js implementation..."
        
        jq '.deployment_validation.simd_features = {
            "binary_present": false,
            "fallback_mode": "nodejs"
        }' "$VALIDATION_RESULTS_FILE" > tmp.json && mv tmp.json "$VALIDATION_RESULTS_FILE"
        
        return 1
    fi
}

# Run performance tests in container
run_performance_tests() {
    log_info "Running performance tests in container..."
    
    # Create test data
    local test_data='{"test": "RTF to Markdown conversion", "content": "\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Times New Roman;}} \\f0\\fs24 Hello World!"}'
    
    # Test API endpoint
    local api_response
    if api_response=$(docker exec "$CONTAINER_NAME" curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$test_data" \
        http://localhost:3000/api/convert/rtf-to-markdown 2>/dev/null); then
        
        log_success "API performance test completed"
        
        # Extract timing information if available
        local response_time
        response_time=$(echo "$api_response" | jq -r '.processing_time // "unknown"' 2>/dev/null || echo "unknown")
        
        jq --arg time "$response_time" --arg response "$api_response" '.deployment_validation.performance_tests = {
            "api_test": "passed",
            "response_time": $time,
            "sample_response": $response
        }' "$VALIDATION_RESULTS_FILE" > tmp.json && mv tmp.json "$VALIDATION_RESULTS_FILE"
        
        return 0
    else
        log_error "API performance test failed"
        
        jq '.deployment_validation.performance_tests = {
            "api_test": "failed",
            "error": "API endpoint not responding"
        }' "$VALIDATION_RESULTS_FILE" > tmp.json && mv tmp.json "$VALIDATION_RESULTS_FILE"
        
        return 1
    fi
}

# Validate security with SIMD optimizations
validate_security() {
    log_info "Validating security with SIMD optimizations..."
    
    # Check health endpoint
    local health_response
    if health_response=$(docker exec "$CONTAINER_NAME" curl -s http://localhost:3000/api/health 2>/dev/null); then
        
        local health_status
        health_status=$(echo "$health_response" | jq -r '.status // "unknown"' 2>/dev/null || echo "unknown")
        
        if [ "$health_status" = "healthy" ]; then
            log_success "Security validation passed - application healthy"
            
            jq --arg status "$health_status" '.deployment_validation.security_validation = {
                "health_check": "passed",
                "status": $status,
                "simd_security": "maintained"
            }' "$VALIDATION_RESULTS_FILE" > tmp.json && mv tmp.json "$VALIDATION_RESULTS_FILE"
            
            return 0
        else
            log_warning "Health check returned: $health_status"
            
            jq --arg status "$health_status" '.deployment_validation.security_validation = {
                "health_check": "warning",
                "status": $status
            }' "$VALIDATION_RESULTS_FILE" > tmp.json && mv tmp.json "$VALIDATION_RESULTS_FILE"
            
            return 1
        fi
    else
        log_error "Health endpoint not accessible"
        
        jq '.deployment_validation.security_validation = {
            "health_check": "failed",
            "error": "Health endpoint not accessible"
        }' "$VALIDATION_RESULTS_FILE" > tmp.json && mv tmp.json "$VALIDATION_RESULTS_FILE"
        
        return 1
    fi
}

# Generate final validation report
generate_final_report() {
    log_info "Generating final validation report..."
    
    # Determine overall status
    local container_status simd_status performance_status security_status overall_status
    container_status=$(jq -r '.deployment_validation.container_status' "$VALIDATION_RESULTS_FILE")
    simd_status=$(jq -r '.deployment_validation.simd_features.binary_present // false' "$VALIDATION_RESULTS_FILE")
    performance_status=$(jq -r '.deployment_validation.performance_tests.api_test // "unknown"' "$VALIDATION_RESULTS_FILE")
    security_status=$(jq -r '.deployment_validation.security_validation.health_check // "unknown"' "$VALIDATION_RESULTS_FILE")
    
    if [ "$container_status" = "running" ] && [ "$performance_status" = "passed" ] && [ "$security_status" = "passed" ]; then
        overall_status="PASSED"
        log_success "Overall deployment validation: PASSED"
    else
        overall_status="FAILED"
        log_error "Overall deployment validation: FAILED"
    fi
    
    # Update final status
    jq --arg status "$overall_status" '.deployment_validation.overall_status = $status' "$VALIDATION_RESULTS_FILE" > tmp.json && mv tmp.json "$VALIDATION_RESULTS_FILE"
    
    # Display summary
    echo ""
    echo "📊 VALIDATION SUMMARY"
    echo "===================="
    echo "Container Status: $container_status"
    echo "SIMD Binary: $simd_status"
    echo "Performance Test: $performance_status"
    echo "Security Check: $security_status"
    echo "Overall Status: $overall_status"
    echo ""
    echo "📄 Detailed results saved to: $VALIDATION_RESULTS_FILE"
}

# Main execution
main() {
    log_info "Starting SIMD deployment validation..."
    
    # Check dependencies
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq is not installed or not in PATH"
        exit 1
    fi
    
    # Initialize
    init_validation_results
    
    # Run validation steps
    local exit_code=0
    
    check_container_status || exit_code=1
    validate_simd_features || exit_code=1
    run_performance_tests || exit_code=1
    validate_security || exit_code=1
    
    # Generate report
    generate_final_report
    
    if [ $exit_code -eq 0 ]; then
        log_success "SIMD deployment validation completed successfully!"
    else
        log_error "SIMD deployment validation completed with errors!"
    fi
    
    exit $exit_code
}

# Run main function
main "$@"
