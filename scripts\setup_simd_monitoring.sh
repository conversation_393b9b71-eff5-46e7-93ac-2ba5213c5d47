#!/bin/bash

# SIMD Monitoring Setup Script
# Sets up comprehensive SIMD performance monitoring infrastructure

set -euo pipefail

echo "🔧 Setting up SIMD Performance Monitoring"
echo "========================================="

# Configuration
MONITORING_DIR="monitoring"
PROMETHEUS_CONFIG="prometheus-simd.yml"
GRAFANA_DASHBOARD="grafana-simd-dashboard.json"
ALERT_RULES="simd_alerts.yml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    local missing_deps=()
    
    if ! command -v docker &> /dev/null; then
        missing_deps+=("docker")
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        missing_deps+=("docker-compose")
    fi
    
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        exit 1
    fi
    
    log_success "All prerequisites satisfied"
}

# Setup monitoring directory structure
setup_monitoring_structure() {
    log_info "Setting up monitoring directory structure..."
    
    mkdir -p "$MONITORING_DIR"/{prometheus,grafana,alerts}
    mkdir -p "$MONITORING_DIR"/data/{prometheus,grafana}
    
    # Set proper permissions
    chmod 755 "$MONITORING_DIR"
    chmod -R 755 "$MONITORING_DIR"/data
    
    log_success "Monitoring directory structure created"
}

# Configure Prometheus for SIMD monitoring
setup_prometheus() {
    log_info "Configuring Prometheus for SIMD monitoring..."
    
    # Copy Prometheus configuration
    if [ -f "$MONITORING_DIR/$PROMETHEUS_CONFIG" ]; then
        cp "$MONITORING_DIR/$PROMETHEUS_CONFIG" "$MONITORING_DIR/prometheus/prometheus.yml"
        log_success "Prometheus configuration deployed"
    else
        log_error "Prometheus configuration file not found: $MONITORING_DIR/$PROMETHEUS_CONFIG"
        return 1
    fi
    
    # Copy alert rules
    if [ -f "$MONITORING_DIR/$ALERT_RULES" ]; then
        cp "$MONITORING_DIR/$ALERT_RULES" "$MONITORING_DIR/prometheus/simd_alerts.yml"
        log_success "SIMD alert rules deployed"
    else
        log_error "Alert rules file not found: $MONITORING_DIR/$ALERT_RULES"
        return 1
    fi
}

# Setup Grafana dashboard
setup_grafana() {
    log_info "Setting up Grafana SIMD dashboard..."
    
    # Create Grafana provisioning directories
    mkdir -p "$MONITORING_DIR/grafana/provisioning"/{dashboards,datasources}
    
    # Copy dashboard
    if [ -f "$MONITORING_DIR/$GRAFANA_DASHBOARD" ]; then
        cp "$MONITORING_DIR/$GRAFANA_DASHBOARD" "$MONITORING_DIR/grafana/provisioning/dashboards/"
        log_success "SIMD dashboard deployed"
    else
        log_error "Grafana dashboard file not found: $MONITORING_DIR/$GRAFANA_DASHBOARD"
        return 1
    fi
    
    # Create datasource configuration
    cat > "$MONITORING_DIR/grafana/provisioning/datasources/prometheus.yml" << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
EOF
    
    # Create dashboard provisioning configuration
    cat > "$MONITORING_DIR/grafana/provisioning/dashboards/dashboards.yml" << EOF
apiVersion: 1

providers:
  - name: 'SIMD Dashboards'
    orgId: 1
    folder: 'SIMD Performance'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards
EOF
    
    log_success "Grafana configuration completed"
}

# Create monitoring docker-compose
create_monitoring_compose() {
    log_info "Creating monitoring docker-compose configuration..."
    
    cat > "$MONITORING_DIR/docker-compose.monitoring.yml" << EOF
version: '3.9'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: legacybridge-prometheus
    ports:
      - "9091:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./prometheus/simd_alerts.yml:/etc/prometheus/simd_alerts.yml
      - ./data/prometheus:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - monitoring
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: legacybridge-grafana
    ports:
      - "3001:3000"
    volumes:
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./data/grafana:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
    networks:
      - monitoring
    restart: unless-stopped
    depends_on:
      - prometheus

  node-exporter:
    image: prom/node-exporter:latest
    container_name: legacybridge-node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - monitoring
    restart: unless-stopped

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: legacybridge-cadvisor
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg
    networks:
      - monitoring
    restart: unless-stopped

networks:
  monitoring:
    driver: bridge
    external: false

volumes:
  prometheus_data:
  grafana_data:
EOF
    
    log_success "Monitoring docker-compose configuration created"
}

# Start monitoring services
start_monitoring() {
    log_info "Starting SIMD monitoring services..."
    
    cd "$MONITORING_DIR"
    
    # Start monitoring stack
    docker-compose -f docker-compose.monitoring.yml up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to start..."
    sleep 30
    
    # Check service health
    local services=("prometheus:9091" "grafana:3001" "node-exporter:9100" "cadvisor:8080")
    local failed_services=()
    
    for service in "${services[@]}"; do
        local name="${service%:*}"
        local port="${service#*:}"
        
        if curl -s "http://localhost:$port" > /dev/null; then
            log_success "$name is running on port $port"
        else
            log_error "$name failed to start on port $port"
            failed_services+=("$name")
        fi
    done
    
    if [ ${#failed_services[@]} -eq 0 ]; then
        log_success "All monitoring services started successfully"
        return 0
    else
        log_error "Failed services: ${failed_services[*]}"
        return 1
    fi
}

# Validate SIMD monitoring setup
validate_monitoring() {
    log_info "Validating SIMD monitoring setup..."
    
    # Check if SIMD metrics are being collected
    local prometheus_url="http://localhost:9091"
    
    # Test Prometheus connectivity
    if ! curl -s "$prometheus_url/api/v1/query?query=up" > /dev/null; then
        log_error "Cannot connect to Prometheus"
        return 1
    fi
    
    # Check for SIMD metrics
    local simd_metrics=("simd_features_available" "simd_utilization_percentage" "cpu_simd_capability_score")
    local missing_metrics=()
    
    for metric in "${simd_metrics[@]}"; do
        if ! curl -s "$prometheus_url/api/v1/query?query=$metric" | grep -q "\"result\""; then
            missing_metrics+=("$metric")
        fi
    done
    
    if [ ${#missing_metrics[@]} -eq 0 ]; then
        log_success "All SIMD metrics are being collected"
    else
        log_warning "Missing SIMD metrics: ${missing_metrics[*]}"
        log_warning "Metrics may appear after the application starts generating data"
    fi
    
    # Check Grafana dashboard
    local grafana_url="http://localhost:3001"
    if curl -s "$grafana_url/api/health" | grep -q "ok"; then
        log_success "Grafana is accessible"
    else
        log_error "Grafana health check failed"
        return 1
    fi
    
    log_success "SIMD monitoring validation completed"
}

# Display setup summary
display_summary() {
    echo ""
    echo "📊 SIMD Monitoring Setup Complete!"
    echo "=================================="
    echo ""
    echo "🔗 Access URLs:"
    echo "   Prometheus: http://localhost:9091"
    echo "   Grafana:    http://localhost:3001 (admin/admin)"
    echo "   Node Exporter: http://localhost:9100"
    echo "   cAdvisor:   http://localhost:8080"
    echo ""
    echo "📈 SIMD Metrics Available:"
    echo "   - SIMD feature availability"
    echo "   - SIMD utilization percentage"
    echo "   - SIMD performance improvements"
    echo "   - SIMD operation latency"
    echo "   - SIMD error rates"
    echo ""
    echo "🚨 Alerts Configured:"
    echo "   - SIMD utilization monitoring"
    echo "   - Performance degradation detection"
    echo "   - Feature availability alerts"
    echo "   - Error rate monitoring"
    echo ""
    echo "📋 Next Steps:"
    echo "   1. Import SIMD dashboard in Grafana"
    echo "   2. Configure alert notifications"
    echo "   3. Start LegacyBridge application"
    echo "   4. Monitor SIMD performance metrics"
}

# Main execution
main() {
    log_info "Starting SIMD monitoring setup..."
    
    check_prerequisites
    setup_monitoring_structure
    setup_prometheus
    setup_grafana
    create_monitoring_compose
    start_monitoring
    validate_monitoring
    display_summary
    
    log_success "SIMD monitoring setup completed successfully!"
}

# Run main function
main "$@"
