# Prometheus Configuration for SIMD Performance Monitoring
# Includes SIMD-specific metrics collection and alerting rules

global:
  scrape_interval: 15s
  evaluation_interval: 15s

# SIMD-specific alerting rules
rule_files:
  - "simd_alerts.yml"

# Scrape configurations
scrape_configs:
  # LegacyBridge application metrics with SIMD data
  - job_name: 'legacybridge-simd'
    static_configs:
      - targets: ['legacybridge:9090']
    scrape_interval: 10s
    metrics_path: '/metrics'
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'legacybridge-app'

  # System-level SIMD monitoring
  - job_name: 'node-exporter-simd'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    metric_relabel_configs:
      # Focus on CPU-related metrics for SIMD analysis
      - source_labels: [__name__]
        regex: 'node_cpu_.*'
        target_label: __name__
        replacement: '${1}'
      - source_labels: [__name__]
        regex: 'node_hwmon_.*'
        target_label: __name__
        replacement: '${1}'

  # Container-specific SIMD metrics
  - job_name: 'cadvisor-simd'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s
    metrics_path: '/metrics'
    metric_relabel_configs:
      # Filter for LegacyBridge container metrics
      - source_labels: [container_label_com_docker_compose_service]
        regex: 'legacybridge'
        target_label: service
        replacement: 'legacybridge'

# Remote write configuration for long-term storage
remote_write:
  - url: "http://prometheus-remote-storage:9201/write"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500

# Storage configuration
storage:
  tsdb:
    retention.time: 30d
    retention.size: 10GB

# External labels for federation
external_labels:
  cluster: 'legacybridge-production'
  environment: 'production'
  simd_enabled: 'true'
