{"dashboard": {"id": null, "title": "LegacyBridge SIMD Performance Dashboard", "tags": ["legacybridge", "simd", "performance"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "SIMD Feature Availability", "type": "stat", "targets": [{"expr": "simd_features_available{available=\"true\"}", "legendFormat": "{{feature}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "CPU SIMD Capability Score", "type": "gauge", "targets": [{"expr": "cpu_simd_capability_score", "legendFormat": "SIMD Score"}], "fieldConfig": {"defaults": {"min": 0, "max": 100, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 40}, {"color": "green", "value": 70}]}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "SIMD Utilization", "type": "timeseries", "targets": [{"expr": "simd_utilization_percentage", "legendFormat": "{{time_window}} utilization"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 4, "title": "SIMD Operation Latency", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, rate(simd_operation_duration_seconds_bucket{simd_enabled=\"true\"}[5m]))", "legendFormat": "95th percentile (SIMD)"}, {"expr": "histogram_quantile(0.95, rate(simd_operation_duration_seconds_bucket{simd_enabled=\"false\"}[5m]))", "legendFormat": "95th percentile (Scalar)"}], "fieldConfig": {"defaults": {"unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 5, "title": "SIMD Performance Improvement", "type": "timeseries", "targets": [{"expr": "simd_performance_improvement_percentage", "legendFormat": "{{document_size_category}} - {{operation_type}}"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 6, "title": "Document Processing Rate", "type": "timeseries", "targets": [{"expr": "rate(documents_processed_total{simd_used=\"true\"}[5m])", "legendFormat": "SIMD enabled"}, {"expr": "rate(documents_processed_total{simd_used=\"false\"}[5m])", "legendFormat": "SIMD disabled"}], "fieldConfig": {"defaults": {"unit": "ops"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 7, "title": "SIMD Error Rate", "type": "timeseries", "targets": [{"expr": "rate(simd_errors_total[5m])", "legendFormat": "{{error_type}}"}], "fieldConfig": {"defaults": {"unit": "ops"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 8, "title": "SIMD Efficiency Score", "type": "stat", "targets": [{"expr": "simd:efficiency_score", "legendFormat": "Efficiency"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 60}, {"color": "green", "value": 80}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 24}}, {"id": 9, "title": "Document Size Distribution", "type": "piechart", "targets": [{"expr": "sum by (size_category) (rate(documents_processed_total[5m]))", "legendFormat": "{{size_category}}"}], "gridPos": {"h": 8, "w": 6, "x": 6, "y": 24}}, {"id": 10, "title": "SIMD vs Scalar Performance Comparison", "type": "bargauge", "targets": [{"expr": "avg by (document_size) (simd_operation_duration_seconds{simd_enabled=\"true\"})", "legendFormat": "SIMD - {{document_size}}"}, {"expr": "avg by (document_size) (simd_operation_duration_seconds{simd_enabled=\"false\"})", "legendFormat": "Scalar - {{document_size}}"}], "fieldConfig": {"defaults": {"unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}], "annotations": {"list": [{"name": "SIMD Alerts", "datasource": "Prometheus", "expr": "ALERTS{component=\"simd\"}", "titleFormat": "{{alertname}}", "textFormat": "{{summary}}"}]}}}